{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "axios": "^1.10.0", "lodash": "^4.17.21", "pinia": "^3.0.3", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/lodash": "^4.17.20", "@types/node": "^22.14.0", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass": "^1.89.2", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}